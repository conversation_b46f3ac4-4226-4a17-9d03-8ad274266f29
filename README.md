# Vegrow Voice UI

## Overview


## Key Features

- **Dynamic Form Builder**: Build complex forms with conditional fields and validation
- **Responsive Design**: Works on all device sizes
- **Component-Based Architecture**: Modular design for maintainability
- **SSO Authentication**: Secure login via Single Sign-On
- **Environment-Specific Configuration**: Development, staging, and production settings
- **User Management**: Manage users with a dedicated tab in the sidebar

## Technology Stack

- **React**: UI library
- **Material UI (MUI)**: Component library for consistent design
- **Formik**: Form management
- **Yup**: Form validation
- **React Router**: Navigation
- **Dayjs**: Date manipulation

## Getting Started

### Prerequisites

- Node.js (v14+)
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>

# Navigate to the project directory
cd vg-voice-ui

# Install dependencies
npm install

# Start the development server
npm start
```

The application will be available at [http://localhost:9000](http://localhost:9000).

## Environment Configuration

The application supports different environments:

- `.env.development` - Local development settings (PORT=9000)
- `.env.staging` - Staging environment settings 
- `.env.production` - Production environment settings

To run with a specific environment configuration:

```bash
# Development (default)
npm start

# Staging
npm run start:staging

# Production
npm run start:production
```

## Project Structure

```
src/
├── components/       # UI components
│   ├── common/       # Shared components
│   ├── form/         # Form builder and fields
│   └── examples/     # Example implementations
├── hooks/            # Custom hooks
├── layouts/          # Page layouts
├── pages/            # Application pages
├── theme/            # Theme configuration
├── utils/            # Utility functions
├── App.js            # App component
└── index.js          # Entry point
```

## Form Builder System

The Form Builder system is a key feature that allows for quick creation of complex forms with conditional logic and validation.

### Basic Usage

```jsx
import { FormBuilder } from '../components/form';

const MyForm = () => {
  // Define fields
  const fields = [
    {
      name: 'email',
      label: 'Email Address',
      type: 'email',
      validations: ['email'],
      helperText: 'Enter your email address',
      gridProps: { md: 6 }
    },
    // Add more fields as needed
  ];

  // Handle form submission
  const handleSubmit = (values) => {
    console.log('Form values:', values);
    // Process form values
  };

  return (
    <FormBuilder
      fields={fields}
      initialValues={{ email: '' }}
      onSubmit={handleSubmit}
      title="Contact Form"
      submitButtonText="Submit"
    />
  );
};
```

### Field Types

The Form Builder supports various field types:

- `text`: Text input
- `email`: Email input
- `password`: Password input
- `number`: Number input
- `tel`: Telephone input
- `select`: Dropdown select
- `multiselect`: Multiple select with chips
- `checkbox`: Checkbox input
- `radio`: Radio button group
- `date`: Date picker
- `file`: File upload

### Conditional Fields

Fields can be conditionally displayed based on the values of other fields:

```jsx
{
  name: 'otherReason',
  label: 'Please specify',
  type: 'text',
  // Only show this field if 'reason' is 'other'
  conditional: (values) => values.reason === 'other'
}
```

## Available Scripts

```bash
# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Lint code
npm run lint
```

## Contributing

1. Ensure any new components follow the existing architecture
2. Maintain components under 200 lines of code
3. Break pages into smaller component chunks
4. Update documentation when making changes
5. Follow the established coding style

## License

[License information]

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Chip,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Alert,
  Collapse,
  Grid,
  TextField,
  TextareaAutosize,
  Menu,
  MenuItem,
  Slider,
  FormControl,
  InputLabel,
  Select,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  Card,
  CardContent,
  LinearProgress,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  Description as DescriptionIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
  Download as DownloadIcon,
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  GetApp as ExportIcon,
  Schedule as ScheduleIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  HourglassEmpty as HourglassEmptyIcon,
  Cancel as CancelIcon,
  Transcribe as TranscribeIcon,
  GraphicEq as WaveformIcon,
  DateRange as DateRangeIcon,
  NoteAdd as NoteAddIcon,
  EditNote as EditNoteIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { formatDate, formatDistance, parseISO, isValid } from 'date-fns';
import { useProject } from '../../contexts/ProjectContext';
import useNotify from '../../hooks/useNotify';
import { apiService } from '../../services/base';

const CallLogs = () => {
  const [calls, setCalls] = useState([]);
  const [filteredCalls, setFilteredCalls] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedCall, setSelectedCall] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });
  const [expandedRow, setExpandedRow] = useState(null);
  const [detailedCalls, setDetailedCalls] = useState({});
  const [remarkModalOpen, setRemarkModalOpen] = useState(false);
  const [currentRemark, setCurrentRemark] = useState('');
  const [selectedCallForRemark, setSelectedCallForRemark] = useState(null);
  const [isSavingRemark, setIsSavingRemark] = useState(false);

  const audioRef = useRef(null);
  const [audioModalOpen, setAudioModalOpen] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [selectedAudio, setSelectedAudio] = useState(null);
  const [isAudioLoading, setIsAudioLoading] = useState(false);
  const { selectedProjectId } = useProject();
  const notify = useNotify();
  // Enhanced filtering state
  const [filtersExpanded, setFiltersExpanded] = useState(false);
  const [filters, setFilters] = useState({
    fromNumber: '',
    toNumber: '',
    status: [],
    persona: [],
    promptType: [],
    dateRange: {
      start: null,
      end: null,
    },
    durationRange: {
      min: '',
      max: '',
    },
    requestId: '',
  });

  // Export state
  const [exportLoading, setExportLoading] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  // Available filter options (will be populated from API)
  const [filterOptions, setFilterOptions] = useState({
    statuses: ['completed', 'failed', 'in_progress', 'queued', 'cancelled', 'no_answer'],
    personas: [],
    promptTypes: [],
  });

  useEffect(() => {
    fetchCalls();
    fetchFilterOptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, rowsPerPage, sortField, sortDirection, selectedProjectId]);

  useEffect(() => {
    applyFilters();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters, calls]);

  const fetchFilterOptions = async () => {
    try {
      // Fetch available personas and prompt types for filters
      const [personasResponse, promptsResponse] = await Promise.all([
        apiService.get('/voice/api/v1/personas'),
        apiService.get('/voice/api/v1/prompts'),
      ]);

      setFilterOptions((prev) => ({
        ...prev,
        personas: personasResponse.items || [],
        promptTypes: promptsResponse.items || [],
      }));
    } catch (error) {
      console.error('Error fetching filter options:', error);
    }
  };

  const applyFilters = () => {
    if (!calls.length) return;

    let filtered = [...calls];

    // From number filter
    if (filters.fromNumber) {
      filtered = filtered.filter(
        (call) => call.from_number && call.from_number.includes(filters.fromNumber)
      );
    }

    // To number filter
    if (filters.toNumber) {
      filtered = filtered.filter(
        (call) => call.to_number && call.to_number.includes(filters.toNumber)
      );
    }

    // Status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter((call) => filters.status.includes(call.status));
    }

    // Persona filter
    if (filters.persona.length > 0) {
      filtered = filtered.filter(
        (call) => call.persona_name && filters.persona.includes(call.persona_name)
      );
    }

    // Date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter((call) => {
        const callDate = new Date(call.created_at);
        const start = filters.dateRange.start ? new Date(filters.dateRange.start) : null;
        const end = filters.dateRange.end ? new Date(filters.dateRange.end) : null;

        if (start && callDate < start) return false;
        if (end && callDate > end) return false;
        return true;
      });
    }

    // Duration range filter
    if (filters.durationRange.min || filters.durationRange.max) {
      filtered = filtered.filter((call) => {
        const duration = call.duration_seconds || 0;
        const min = filters.durationRange.min ? parseInt(filters.durationRange.min) : 0;
        const max = filters.durationRange.max ? parseInt(filters.durationRange.max) : Infinity;

        return duration >= min && duration <= max;
      });
    }

    // Call ID filter
    if (filters.requestId) {
      filtered = filtered.filter(
        (call) => call.request_id && call.request_id.includes(filters.requestId)
      );
    }

    setFilteredCalls(filtered);
    setTotalCount(filtered.length);
  };

  const fetchCalls = useCallback(async () => {
    if (!selectedProjectId) return; // Don't fetch if no project is selected

    setLoading(true);
    try {
      const params = {
        sort: sortField,
        order: sortDirection,
        project_id: selectedProjectId,
      };

      const response = await apiService.get('/voice/api/v1/calls', { params });
      const items = response.items || [];
      setCalls(items);
      setFilteredCalls(items);
      setTotalCount(items.length);
    } catch (error) {
      console.error('Error fetching calls:', error);
      showAlert('Error fetching call data', 'error');
    } finally {
      setLoading(false);
    }
  }, [sortField, sortDirection, selectedProjectId]); // Added selectedProjectId to dependencies

  const fetchCallDetails = async (callId) => {
    try {
      const response = await apiService.get(`/voice/api/v1/calls/${callId}`);
      setDetailedCalls((prev) => ({
        ...prev,
        [callId]: response,
      }));
    } catch (error) {
      console.error('Error fetching call details:', error);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSortChange = (field) => {
    const isAsc = sortField === field && sortDirection === 'asc';
    setSortDirection(isAsc ? 'desc' : 'asc');
    setSortField(field);
  };

  const toggleRowExpansion = (callId) => {
    if (expandedRow !== callId) {
      fetchCallDetails(callId);
    }
    setExpandedRow(expandedRow === callId ? null : callId);
  };

  const handleMenuOpen = (event, call) => {
    setAnchorEl(event.currentTarget);
    setSelectedCall(call);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const showAlert = (message, severity) => {
    setAlert({ show: true, message, severity });
    setTimeout(() => {
      setAlert({ show: false, message: '', severity: 'success' });
    }, 5000);
  };

  // Handle remark modal open/close
  const handleRemarkModalOpen = (call, e) => {
    if (e) {
      e.stopPropagation();
    }
    setSelectedCallForRemark(call);
    setCurrentRemark(call.remarks || '');
    setRemarkModalOpen(true);
  };

  const handleRemarkModalClose = () => {
    setRemarkModalOpen(false);
    setSelectedCallForRemark(null);
    setCurrentRemark('');
  };

  // Save remark to API
  const handleSaveRemark = async () => {
    if (!selectedCallForRemark) return;

    setIsSavingRemark(true);
    try {
      await apiService.put(`/voice/api/v1/calls/${selectedCallForRemark.id}`, {
        remarks: currentRemark,
      });

      // Update the call in the local state
      setCalls(
        calls.map((call) =>
          call.id === selectedCallForRemark.id ? { ...call, remarks: currentRemark } : call
        )
      );

      handleRemarkModalClose();
      showAlert('Remark saved successfully', 'success');
    } catch (error) {
      console.error('Error saving remark:', error);
      showAlert('Failed to save remark', 'error');
    } finally {
      setIsSavingRemark(false);
    }
  };

  // Enhanced status functions with minimal color scheme
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#e8f5e8'; // Very light green background
      case 'failed':
        return '#ffeaea'; // Very light red background
      case 'in_progress':
        return '#fff3e0'; // Very light orange background
      case 'queued':
        return '#e3f2fd'; // Very light blue background
      case 'cancelled':
        return '#f5f5f5'; // Light grey background
      case 'no_answer':
        return '#fff3e0'; // Very light orange background
      default:
        return '#f5f5f5'; // Light grey background
    }
  };

  const getStatusTextColor = (status) => {
    switch (status) {
      case 'completed':
        return '#2e7d32'; // Dark green text
      case 'failed':
        return '#d32f2f'; // Dark red text
      case 'in_progress':
        return '#f57c00'; // Dark orange text
      case 'queued':
        return '#1976d2'; // Dark blue text
      case 'cancelled':
        return '#616161'; // Dark grey text
      case 'no_answer':
        return '#f57c00'; // Dark orange text
      default:
        return '#616161'; // Dark grey text
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon fontSize='small' />;
      case 'failed':
        return <ErrorIcon fontSize='small' />;
      case 'in_progress':
        return <HourglassEmptyIcon fontSize='small' />;
      case 'queued':
        return <ScheduleIcon fontSize='small' />;
      case 'cancelled':
        return <CancelIcon fontSize='small' />;
      case 'no_answer':
        return <WarningIcon fontSize='small' />;
      default:
        return null;
    }
  };

  const formatStatusLabel = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'in_progress':
        return 'In Progress';
      case 'queued':
        return 'Queued';
      case 'cancelled':
        return 'Cancelled';
      case 'no_answer':
        return 'No Answer';
      default:
        return status?.charAt(0).toUpperCase() + status?.slice(1) || '';
    }
  };

  // Enhanced date formatting functions
  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';

    try {
      const date = parseISO(dateString);
      if (!isValid(date)) return 'Invalid Date';

      // Format as "May 28, 2025 – 8:03 AM"
      return formatDate(date, 'MMM dd, yyyy – h:mm a');
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  const formatRelativeTime = (dateString) => {
    if (!dateString) return '';

    try {
      const date = parseISO(dateString);
      if (!isValid(date)) return '';

      return formatDistance(date, new Date(), { addSuffix: true });
    } catch (error) {
      console.error('Error formatting relative time:', error);
      return '';
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Filter utility functions
  const clearAllFilters = () => {
    setFilters({
      fromNumber: '',
      toNumber: '',
      status: [],
      persona: [],
      promptType: [],
      dateRange: {
        start: null,
        end: null,
      },
      durationRange: {
        min: '',
        max: '',
      },
      requestId: '',
    });
  };

  const hasActiveFilters = () => {
    return (
      filters.fromNumber ||
      filters.toNumber ||
      filters.status.length > 0 ||
      filters.persona.length > 0 ||
      filters.promptType.length > 0 ||
      filters.dateRange.start ||
      filters.dateRange.end ||
      filters.durationRange.min ||
      filters.durationRange.max ||
      filters.requestId
    );
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.fromNumber) count++;
    if (filters.toNumber) count++;
    if (filters.status.length > 0) count++;
    if (filters.persona.length > 0) count++;
    if (filters.promptType.length > 0) count++;
    if (filters.dateRange.start || filters.dateRange.end) count++;
    if (filters.durationRange.min || filters.durationRange.max) count++;
    if (filters.requestId) count++;
    return count;
  };

  // Export functionality
  const handleExport = async (format = 'csv') => {
    setExportLoading(true);
    setExportProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setExportProgress((prev) => Math.min(prev + 10, 90));
      }, 100);

      // Prepare data for export
      const exportData = filteredCalls.map((call) => ({
        'Call ID': call.unique_id || `CALL-${call.id}`,
        'Request ID': call.request_id || 'N/A',
        'Date/Time': formatDateTime(call.created_at),
        From: call.from_number || 'N/A',
        To: call.to_number,
        Persona: call.persona_name || 'N/A',
        Duration: formatDuration(call.duration_seconds),
        Status: formatStatusLabel(call.status),
        Summary: call.summary || 'N/A',
        Error: call.failure_reason || 'N/A',
      }));

      // Convert to CSV
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map((row) =>
          headers
            .map((header) => `"${(row[header] || '').toString().replace(/"/g, '""')}"`)
            .join(',')
        ),
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `call-logs-${formatDate(new Date(), 'yyyy-MM-dd')}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      clearInterval(progressInterval);
      setExportProgress(100);
      notify(`Successfully exported ${exportData.length} call logs`, 'success');
    } catch (error) {
      console.error('Export error:', error);
      showAlert('Failed to export call logs', 'error');
    } finally {
      setTimeout(() => {
        setExportLoading(false);
        setExportProgress(0);
      }, 1000);
    }
  };

  const handleRetryCall = async (callId) => {
    try {
      await apiService.post(`/voice/api/v1/calls/${callId}/retry`);
      showAlert('Call retry initiated successfully', 'success');
      // Refresh the calls list after a short delay
      setTimeout(() => {
        fetchCalls();
      }, 1000);
    } catch (error) {
      console.error('Error retrying call:', error);
      showAlert('Failed to retry call', 'error');
    }
  };

  const formatTime = (seconds) => {
    if (!seconds || isNaN(seconds) || !isFinite(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getAudioDuration = async (url) => {
    try {
      const response = await fetch(url);
      const arrayBuffer = await response.arrayBuffer();
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      return audioBuffer.duration;
    } catch (error) {
      console.error('Error getting audio duration:', error);
      return null;
    }
  };

  const loadAudio = async (url) => {
    try {
      // First try to fetch the audio file to check its content type
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');

      if (!contentType?.includes('audio/')) {
        console.warn('Warning: Response may not be an audio file. Content-Type:', contentType);
      }

      // Get duration using Web Audio API
      const duration = await getAudioDuration(url);
      if (duration) {
        setDuration(duration);
      }

      // Create a new audio element
      const audio = new Audio();

      // Add event listeners
      audio.addEventListener('timeupdate', handleTimeUpdate);
      audio.addEventListener('loadedmetadata', handleLoadedMetadata);
      audio.addEventListener('canplaythrough', () => {
        setIsAudioLoading(false);
      });
      audio.addEventListener('ended', () => setIsPlaying(false));

      // Create a promise to handle the loading
      const loadPromise = new Promise((resolve, reject) => {
        const handleMetadata = () => {
          audio.removeEventListener('loadedmetadata', handleMetadata);
          audio.removeEventListener('error', handleError);
          clearTimeout(timeout);
          resolve();
        };

        const handleError = (event) => {
          audio.removeEventListener('loadedmetadata', handleMetadata);
          audio.removeEventListener('error', handleError);
          clearTimeout(timeout);
          reject(event);
        };

        audio.addEventListener('loadedmetadata', handleMetadata);
        audio.addEventListener('error', handleError);

        // Set a timeout to reject if loading takes too long
        const timeout = setTimeout(() => {
          audio.removeEventListener('loadedmetadata', handleMetadata);
          audio.removeEventListener('error', handleError);
          reject(new Error('Audio loading timed out'));
        }, 10000); // 10 second timeout
      });

      // Set source and start loading
      audio.src = url;
      audio.preload = 'metadata';
      audio.load();

      // Wait for the loading to complete
      await loadPromise;

      return audio;
    } catch (error) {
      console.error('Error loading audio:', error);
      throw error;
    }
  };

  const handleTimeUpdate = () => {
    if (
      audioRef.current &&
      !isNaN(audioRef.current.currentTime) &&
      isFinite(audioRef.current.currentTime)
    ) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      console.log('Audio metadata loaded:', {
        duration: audioRef.current.duration,
        readyState: audioRef.current.readyState,
        networkState: audioRef.current.networkState,
      });

      // Try to get duration
      const duration = audioRef.current.duration;
      if (!isNaN(duration) && isFinite(duration) && duration > 0) {
        console.log('Setting duration from metadata:', duration);
        setDuration(duration);
      }
    }
  };

  const handleDownload = async () => {
    if (selectedAudio?.url) {
      try {
        setIsAudioLoading(true);
        // Fetch the audio file
        const response = await fetch(selectedAudio.url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Get the file as a blob
        const blob = await response.blob();

        // Create a URL for the blob
        const blobUrl = window.URL.createObjectURL(blob);

        // Create a temporary link element
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = `call-recording-${selectedAudio.id}.mp3`;

        // Append to body, click, and remove
        document.body.appendChild(link);
        link.click();

        // Clean up
        document.body.removeChild(link);
        window.URL.revokeObjectURL(blobUrl);

        showAlert('Download started', 'success');
      } catch (error) {
        console.error('Error downloading audio:', error);
        showAlert('Failed to download audio file', 'error');
      } finally {
        setIsAudioLoading(false);
      }
    }
  };

  const handleAudioError = (event) => {
    // Get the audio element that triggered the error
    const audioElement = event.target || event.currentTarget;

    console.error('Audio error details:', {
      event: event,
      audioElement: audioElement,
      url: selectedAudio?.url,
      readyState: audioElement?.readyState,
      networkState: audioElement?.networkState,
      errorCode: audioElement?.error?.code,
      errorMessage: audioElement?.error?.message,
      error: audioElement?.error,
    });

    let errorMessage = 'Error loading audio. ';

    if (audioElement?.error) {
      switch (audioElement.error.code) {
        case 1:
          errorMessage += 'The audio loading was aborted.';
          break;
        case 2:
          errorMessage += 'Network error occurred. Please check your connection.';
          break;
        case 3:
          errorMessage += 'Audio decoding failed. The file may be corrupted.';
          break;
        case 4:
          errorMessage +=
            'Audio format not supported or file not accessible. Please check if the file is a valid audio file.';
          break;
        default:
          errorMessage += 'Unknown error occurred.';
      }
    } else {
      errorMessage +=
        'Unable to load audio file. Please check if the URL is correct and accessible.';
    }

    showAlert(errorMessage, 'error');
    setAudioModalOpen(false);
    setIsAudioLoading(false);
  };

  // Handle audio playback when modal is opened or audio source changes
  useEffect(() => {
    if (audioModalOpen && selectedAudio?.url) {
      // Clean up any existing audio
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = '';
      }

      // Reset state
      setIsAudioLoading(true);
      setCurrentTime(0);
      setDuration(0);
      setIsPlaying(false);

      // Load the new audio
      loadAudio(selectedAudio.url)
        .then((audio) => {
          audioRef.current = audio;

          // Add event listeners
          audio.addEventListener('error', handleAudioError);

          // Try to get duration after successful load
          if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
            setDuration(audio.duration);
          }
        })
        .catch((error) => {
          console.error('Failed to load audio:', error);
          handleAudioError(error);
        })
        .finally(() => {
          setIsAudioLoading(false);
        });

      return () => {
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current.src = '';
          audioRef.current.removeEventListener('timeupdate', handleTimeUpdate);
          audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);
          audioRef.current.removeEventListener('canplaythrough', () => {
            setIsAudioLoading(false);
          });
          audioRef.current.removeEventListener('ended', () => setIsPlaying(false));
          audioRef.current.removeEventListener('error', handleAudioError);
        }
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [audioModalOpen, selectedAudio]);

  const handlePlayPause = async () => {
    if (!audioRef.current) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        // Ensure audio is loaded before playing
        if (audioRef.current.readyState < 2) {
          await new Promise((resolve) => {
            audioRef.current.addEventListener('canplay', resolve, { once: true });
          });
        }

        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('Error playing audio:', error);
      showAlert('Error playing audio. Please try again.', 'error');
      setIsPlaying(false);
    }
  };

  const handleSliderChange = (event, newValue) => {
    if (audioRef.current && !isNaN(newValue) && isFinite(newValue)) {
      try {
        audioRef.current.currentTime = newValue;
        setCurrentTime(newValue);
      } catch (error) {
        console.error('Error setting audio time:', error);
      }
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth='xl' sx={{ py: 3 }}>
        {/* Header Section */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1.5 }}>
            <Button
              variant='outlined'
              startIcon={<ExportIcon />}
              onClick={() => handleExport('csv')}
              disabled={exportLoading || filteredCalls.length === 0}
              sx={{
                minWidth: 120,
                borderColor: 'grey.300',
                color: 'text.primary',
                '&:hover': {
                  borderColor: 'primary.main',
                  backgroundColor: 'primary.50',
                },
              }}
            >
              {exportLoading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CircularProgress size={16} />
                  {exportProgress}%
                </Box>
              ) : (
                'Export'
              )}
            </Button>
            <Button
              variant='outlined'
              startIcon={<RefreshIcon />}
              onClick={fetchCalls}
              disabled={loading}
              sx={{
                borderColor: 'grey.300',
                color: 'text.primary',
                '&:hover': {
                  borderColor: 'primary.main',
                  backgroundColor: 'primary.50',
                },
              }}
            >
              Refresh
            </Button>
          </Box>
        </Box>

        {/* Export Progress */}
        {exportLoading && (
          <Box sx={{ mb: 2 }}>
            <LinearProgress variant='determinate' value={exportProgress} />
            <Typography variant='caption' color='text.secondary' sx={{ mt: 0.5 }}>
              Exporting {filteredCalls.length} call logs...
            </Typography>
          </Box>
        )}

        {/* Alert Messages */}
        {alert.show && (
          <Alert
            severity={alert.severity}
            sx={{ mb: 2 }}
            onClose={() => setAlert({ ...alert, show: false })}
          >
            {alert.message}
          </Alert>
        )}

        {/* Enhanced Filtering Panel */}
        <Paper
          sx={{
            mb: 3,
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'grey.200',
            boxShadow: '0 1px 3px rgba(0,0,0,0.08)',
          }}
        >
          <Accordion
            expanded={filtersExpanded}
            onChange={(event, isExpanded) => setFiltersExpanded(isExpanded)}
            sx={{
              boxShadow: 'none',
              '&:before': { display: 'none' },
              borderRadius: 2,
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                px: 3,
                py: 1.5,
                borderBottom: filtersExpanded ? '1px solid' : 'none',
                borderColor: 'grey.200',
                minHeight: '56px',
                '&.Mui-expanded': {
                  minHeight: '56px',
                },
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  width: '100%',
                  flexWrap: 'wrap',
                }}
              >
                <FilterListIcon sx={{ color: 'grey.600', fontSize: 20 }} />
                <Typography variant='subtitle1' sx={{ fontWeight: 500, color: 'text.primary' }}>
                  Filters
                </Typography>
                {hasActiveFilters() && (
                  <Chip
                    label={getActiveFilterCount()}
                    size='small'
                    sx={{
                      backgroundColor: 'primary.50',
                      color: 'primary.main',
                      fontWeight: 500,
                      height: 20,
                      fontSize: '0.75rem',
                    }}
                  />
                )}

                {/* Compact inline filters when collapsed */}
                {/* {!filtersExpanded && (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1.5,
                      ml: 2,
                      flex: 1,
                      flexWrap: 'nowrap',
                    }}
                  >
                    <TextField
                      variant='outlined'
                      size='small'
                      value={filters.fromNumber}
                      onChange={(e) =>
                        setFilters((prev) => ({ ...prev, fromNumber: e.target.value }))
                      }
                      placeholder='From Number'
                      sx={{ width: 140, flexShrink: 0, pt: 1.5 }}
                      onClick={(e) => e.stopPropagation()}
                    />
                    <TextField
                      variant='outlined'
                      size='small'
                      value={filters.toNumber}
                      onChange={(e) =>
                        setFilters((prev) => ({ ...prev, toNumber: e.target.value }))
                      }
                      placeholder='To Number'
                      sx={{ width: 140, flexShrink: 0, pt: 1.5 }}
                      onClick={(e) => e.stopPropagation()}
                    />
                    <Box
                      onClick={(e) => e.stopPropagation()}
                      onMouseDown={(e) => e.stopPropagation()}
                      sx={{ width: 180, flexShrink: 0 }}
                    >
                      <DatePicker
                        label='Start Date'
                        value={filters.dateRange.start}
                        onChange={(newValue) =>
                          setFilters((prev) => ({
                            ...prev,
                            dateRange: { ...prev.dateRange, start: newValue },
                          }))
                        }
                        disablePortal
                        slotProps={{
                          textField: {
                            size: 'small',
                            onClick: (e) => e.stopPropagation(),
                            onMouseDown: (e) => e.stopPropagation(),
                            onFocus: (e) => e.stopPropagation(),
                            InputProps: {
                              startAdornment: <DateRangeIcon color='action' sx={{ mr: 1 }} />,
                            },
                          },
                          popper: {
                            disablePortal: true,
                          },
                        }}
                        sx={{ width: '100%' }}
                      />
                    </Box>
                    <Box
                      onClick={(e) => e.stopPropagation()}
                      onMouseDown={(e) => e.stopPropagation()}
                      sx={{ width: 180, flexShrink: 0 }}
                    >
                      <DatePicker
                        label='End Date'
                        value={filters.dateRange.end}
                        onChange={(newValue) =>
                          setFilters((prev) => ({
                            ...prev,
                            dateRange: { ...prev.dateRange, end: newValue },
                          }))
                        }
                        disablePortal
                        slotProps={{
                          textField: {
                            size: 'small',
                            onClick: (e) => e.stopPropagation(),
                            onMouseDown: (e) => e.stopPropagation(),
                            onFocus: (e) => e.stopPropagation(),
                            InputProps: {
                              startAdornment: <DateRangeIcon color='action' sx={{ mr: 1 }} />,
                            },
                          },
                          popper: {
                            disablePortal: true,
                          },
                        }}
                        sx={{ width: '100%' }}
                      />
                    </Box>
                  </Box>
                )} */}

                <Box sx={{ flexGrow: 1 }} />
                {hasActiveFilters() && (
                  <Tooltip title='Clear all filters'>
                    <IconButton
                      size='small'
                      onClick={(e) => {
                        e.stopPropagation();
                        clearAllFilters();
                      }}
                      sx={{ mr: 1 }}
                    >
                      <ClearIcon fontSize='small' />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>
            </AccordionSummary>
            <AccordionDetails sx={{ px: 3, py: 2 }}>
              <Grid container spacing={3}>
                {/* Row 1: Primary filters - From Number, To Number, Start Date, End Date */}
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label='From Number'
                    variant='outlined'
                    size='small'
                    fullWidth
                    value={filters.fromNumber}
                    onChange={(e) =>
                      setFilters((prev) => ({ ...prev, fromNumber: e.target.value }))
                    }
                    placeholder='Enter phone number'
                    sx={{ minWidth: 120, maxWidth: 140 }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label='To Number'
                    variant='outlined'
                    size='small'
                    fullWidth
                    value={filters.toNumber}
                    onChange={(e) => setFilters((prev) => ({ ...prev, toNumber: e.target.value }))}
                    placeholder='Enter phone number'
                    sx={{ minWidth: 120, maxWidth: 140 }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <DatePicker
                    label='Start Date'
                    value={filters.dateRange.start}
                    onChange={(newValue) =>
                      setFilters((prev) => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, start: newValue },
                      }))
                    }
                    slotProps={{
                      textField: {
                        size: 'small',
                        fullWidth: true,
                        InputProps: {
                          startAdornment: <DateRangeIcon color='action' sx={{ mr: 1 }} />,
                        },
                      },
                    }}
                    sx={{ minWidth: 120, maxWidth: 190 }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <DatePicker
                    label='End Date'
                    value={filters.dateRange.end}
                    onChange={(newValue) =>
                      setFilters((prev) => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, end: newValue },
                      }))
                    }
                    slotProps={{
                      textField: {
                        size: 'small',
                        fullWidth: true,
                        InputProps: {
                          startAdornment: <DateRangeIcon color='action' sx={{ mr: 1 }} />,
                        },
                      },
                    }}
                    sx={{ minWidth: 120, maxWidth: 190 }}
                  />
                </Grid>

                {/* Row 2: Secondary filters - Status, Persona, Duration Range */}
                <Grid item xs={12} sm={6} md={3} sx={{ minWidth: 120, maxWidth: 120 }}>
                  <FormControl fullWidth size='small'>
                    <InputLabel>Status</InputLabel>
                    <Select
                      multiple
                      value={filters.status}
                      onChange={(e) => setFilters((prev) => ({ ...prev, status: e.target.value }))}
                      label='Status'
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={formatStatusLabel(value)} size='small' />
                          ))}
                        </Box>
                      )}
                    >
                      {filterOptions.statuses.map((status) => (
                        <MenuItem key={status} value={status}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getStatusIcon(status)}
                            {formatStatusLabel(status)}
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={3} sx={{ minWidth: 120, maxWidth: 140 }}>
                  <FormControl fullWidth size='small'>
                    <InputLabel>Persona</InputLabel>
                    <Select
                      multiple
                      value={filters.persona}
                      onChange={(e) => setFilters((prev) => ({ ...prev, persona: e.target.value }))}
                      label='Persona'
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size='small' />
                          ))}
                        </Box>
                      )}
                    >
                      {filterOptions.personas.map((persona) => (
                        <MenuItem key={persona.id} value={persona.name}>
                          {persona.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label='Min Duration (seconds)'
                    variant='outlined'
                    size='small'
                    fullWidth
                    type='number'
                    value={filters.durationRange.min}
                    onChange={(e) =>
                      setFilters((prev) => ({
                        ...prev,
                        durationRange: { ...prev.durationRange, min: e.target.value },
                      }))
                    }
                    placeholder='0'
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label='Max Duration (seconds)'
                    variant='outlined'
                    size='small'
                    fullWidth
                    type='number'
                    value={filters.durationRange.max}
                    onChange={(e) =>
                      setFilters((prev) => ({
                        ...prev,
                        durationRange: { ...prev.durationRange, max: e.target.value },
                      }))
                    }
                    placeholder='∞'
                  />
                </Grid>

                {/* Row 3: Additional filters - Call ID */}
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label='Call ID'
                    variant='outlined'
                    size='small'
                    fullWidth
                    value={filters.requestId}
                    onChange={(e) => setFilters((prev) => ({ ...prev, requestId: e.target.value }))}
                    placeholder='Search by Call ID'
                  />
                </Grid>
                <Button
                  variant='outlined'
                  size='small'
                  onClick={clearAllFilters}
                  disabled={!hasActiveFilters()}
                  startIcon={<ClearIcon />}
                  sx={{ minWidth: 120, maxWidth: 140, maxHeight: 40 }}
                >
                  Clear All Filters
                </Button>
                <Typography variant='body2' color='text.secondary' sx={{ alignSelf: 'center' }}>
                  Showing {filteredCalls.length} of {calls.length} calls
                </Typography>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Paper>

        {/* Enhanced Table */}
        <Paper
          sx={{
            mb: 3,
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'grey.200',
            boxShadow: '0 1px 3px rgba(0,0,0,0.08)',
            overflow: 'hidden',
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#fafafa' }}>
                  <TableCell
                    width='40px'
                    sx={{
                      fontWeight: 600,
                      color: 'text.secondary',
                      fontSize: '0.875rem',
                      py: 2,
                    }}
                  />
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      minWidth: 120,
                      color: 'text.secondary',
                      fontSize: '0.875rem',
                      py: 2,
                    }}
                  >
                    Call ID
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      minWidth: 180,
                      color: 'text.secondary',
                      fontSize: '0.875rem',
                      py: 2,
                    }}
                  >
                    <Box
                      sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                      onClick={() => handleSortChange('created_at')}
                    >
                      Date/Time
                      {sortField === 'created_at' && (
                        <Box component='span' sx={{ ml: 0.5, color: 'primary.main' }}>
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </Box>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      minWidth: 120,
                      color: 'text.secondary',
                      fontSize: '0.875rem',
                      py: 2,
                    }}
                  >
                    From → To
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      minWidth: 100,
                      color: 'text.secondary',
                      fontSize: '0.875rem',
                      py: 2,
                    }}
                  >
                    Status
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      minWidth: 180,
                      color: 'text.secondary',
                      fontSize: '0.875rem',
                      py: 2,
                    }}
                  >
                    Media & Duration
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      minWidth: 200,
                      color: 'text.secondary',
                      fontSize: '0.875rem',
                      py: 2,
                    }}
                  >
                    Summary
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      minWidth: 120,
                      color: 'text.secondary',
                      fontSize: '0.875rem',
                      py: 2,
                    }}
                  >
                    Remarks
                  </TableCell>
                  <TableCell
                    sx={{
                      fontWeight: 600,
                      width: 60,
                      color: 'text.secondary',
                      fontSize: '0.875rem',
                      py: 2,
                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} align='center' sx={{ py: 4 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          gap: 2,
                        }}
                      >
                        <CircularProgress size={32} />
                        <Typography variant='body2' color='text.secondary'>
                          Loading call logs...
                        </Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : filteredCalls.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align='center' sx={{ py: 6 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          gap: 2,
                        }}
                      >
                        <Typography variant='h6' color='text.secondary'>
                          No calls found
                        </Typography>
                        <Typography variant='body2' color='text.secondary'>
                          {hasActiveFilters()
                            ? 'Try adjusting your filters to see more results'
                            : 'No call logs available yet'}
                        </Typography>
                        {hasActiveFilters() && (
                          <Button
                            variant='outlined'
                            size='small'
                            onClick={clearAllFilters}
                            startIcon={<ClearIcon />}
                          >
                            Clear Filters
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredCalls
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((call) => (
                      <React.Fragment key={call.id}>
                        <TableRow
                          hover
                          onClick={() => toggleRowExpansion(call.id)}
                          sx={{
                            cursor: 'pointer',
                            backgroundColor: expandedRow === call.id ? '#f8f9fa' : 'inherit',
                            borderBottom: '1px solid #f0f0f0',
                            '&:hover': {
                              backgroundColor: expandedRow === call.id ? '#f0f2f5' : '#f8f9fa',
                            },
                            '&:last-child td': {
                              borderBottom: 'none',
                            },
                            transition: 'background-color 0.2s ease',
                          }}
                        >
                          <TableCell sx={{ py: 2, width: 40 }}>
                            <Tooltip
                              title={
                                expandedRow === call.id ? 'Collapse details' : 'Expand details'
                              }
                            >
                              <IconButton size='small' sx={{ color: 'grey.600' }}>
                                {expandedRow === call.id ? (
                                  <KeyboardArrowUpIcon />
                                ) : (
                                  <KeyboardArrowDownIcon />
                                )}
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                          <TableCell sx={{ py: 2 }}>
                            <Typography
                              variant='body2'
                              sx={{
                                fontFamily: 'monospace',
                                fontSize: '0.8rem',
                                color: 'text.primary',
                                fontWeight: 500,
                              }}
                            >
                              {call.unique_id || (
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                  <Typography
                                    variant='caption'
                                    sx={{
                                      color: 'text.secondary',
                                      fontFamily: 'monospace',
                                      fontSize: '0.75rem',
                                    }}
                                  >
                                    CALL-{call.id}
                                  </Typography>
                                  <Tooltip title='Auto-generated ID'>
                                    <InfoIcon sx={{ fontSize: '14px', color: 'grey.400' }} />
                                  </Tooltip>
                                </Box>
                              )}
                            </Typography>
                          </TableCell>

                          {/* Enhanced Date/Time Column */}
                          <TableCell sx={{ py: 2 }}>
                            <Box>
                              <Typography
                                variant='body2'
                                sx={{
                                  fontWeight: 500,
                                  color: 'text.primary',
                                  fontSize: '0.875rem',
                                }}
                              >
                                {formatDateTime(call.created_at)}
                              </Typography>
                              <Typography
                                variant='caption'
                                sx={{
                                  color: 'text.secondary',
                                  fontSize: '0.75rem',
                                }}
                              >
                                {formatRelativeTime(call.created_at)}
                              </Typography>
                            </Box>
                          </TableCell>

                          {/* Combined From → To Column */}
                          <TableCell sx={{ py: 2 }}>
                            <Box>
                              <Typography
                                variant='body2'
                                sx={{
                                  fontWeight: 500,
                                  color: 'text.primary',
                                  fontSize: '0.875rem',
                                }}
                              >
                                {call.from_number || 'N/A'}
                              </Typography>
                              <Typography
                                variant='body2'
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.5,
                                  color: 'text.secondary',
                                  fontSize: '0.875rem',
                                }}
                              >
                                → {call.to_number}
                              </Typography>
                              {call.persona_name && (
                                <Typography
                                  variant='caption'
                                  sx={{
                                    color: 'text.secondary',
                                    fontSize: '0.75rem',
                                  }}
                                >
                                  via {call.persona_name}
                                </Typography>
                              )}
                            </Box>
                          </TableCell>

                          {/* Enhanced Status Column with Error Display */}
                          <TableCell sx={{ py: 2 }}>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                              <Chip
                                icon={getStatusIcon(call.status)}
                                label={formatStatusLabel(call.status)}
                                size='small'
                                sx={{
                                  backgroundColor: getStatusColor(call.status),
                                  color: getStatusTextColor(call.status),
                                  fontWeight: 500,
                                  fontSize: '0.75rem',
                                  height: 24,
                                  border: 'none',
                                  '& .MuiChip-icon': {
                                    fontSize: '14px',
                                    color: getStatusTextColor(call.status),
                                  },
                                  '& .MuiChip-label': {
                                    px: 1,
                                  },
                                }}
                              />
                              {call.status === 'failed' && call.failure_reason && (
                                <Tooltip title={call.failure_reason}>
                                  <Typography
                                    variant='caption'
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: 0.5,
                                      cursor: 'help',
                                      color: '#d32f2f',
                                      fontSize: '0.7rem',
                                    }}
                                  >
                                    <ErrorIcon sx={{ fontSize: '12px' }} />
                                    API Error
                                  </Typography>
                                </Tooltip>
                              )}
                            </Box>
                          </TableCell>

                          {/* Consolidated Media & Duration Column */}
                          <TableCell sx={{ py: 2 }}>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                              {/* Duration */}
                              <Typography
                                variant='body2'
                                sx={{
                                  fontWeight: 500,
                                  color: 'text.primary',
                                  fontSize: '0.875rem',
                                }}
                              >
                                {formatDuration(call.duration_seconds)}
                              </Typography>

                              {/* Audio Player */}
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {isAudioLoading ? (
                                  <CircularProgress size={18} />
                                ) : (
                                  <Tooltip
                                    title={
                                      detailedCalls[call.id]?.audio_url
                                        ? 'Play recording'
                                        : 'No audio available'
                                    }
                                  >
                                    <IconButton
                                      size='small'
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        if (detailedCalls[call.id]?.audio_url) {
                                          setSelectedAudio({
                                            url: detailedCalls[call.id].audio_url,
                                            id: call.id,
                                          });
                                          setAudioModalOpen(true);
                                        }
                                      }}
                                      disabled={!detailedCalls[call.id]?.audio_url}
                                      sx={{
                                        color: detailedCalls[call.id]?.audio_url
                                          ? '#1976d2'
                                          : 'grey.400',
                                        p: 0.5,
                                      }}
                                    >
                                      <WaveformIcon sx={{ fontSize: '16px' }} />
                                    </IconButton>
                                  </Tooltip>
                                )}

                                {/* Transcript Link */}
                                {detailedCalls[call.id]?.transcript_url && (
                                  <Tooltip title='View transcript'>
                                    <IconButton
                                      size='small'
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        window.open(
                                          detailedCalls[call.id].transcript_url,
                                          '_blank'
                                        );
                                      }}
                                      sx={{
                                        color: '#1976d2',
                                        p: 0.5,
                                      }}
                                    >
                                      <TranscribeIcon sx={{ fontSize: '16px' }} />
                                    </IconButton>
                                  </Tooltip>
                                )}
                              </Box>
                            </Box>
                          </TableCell>

                          {/* Enhanced Summary Column */}
                          <TableCell sx={{ py: 2 }}>
                            <Box>
                              {call.summary ? (
                                <Typography
                                  variant='body2'
                                  sx={{
                                    display: '-webkit-box',
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: 'vertical',
                                    overflow: 'hidden',
                                    lineHeight: 1.4,
                                    color: 'text.primary',
                                    fontSize: '0.875rem',
                                  }}
                                >
                                  {call.summary}
                                </Typography>
                              ) : (
                                <Typography
                                  variant='body2'
                                  sx={{
                                    fontStyle: 'italic',
                                    color: 'text.secondary',
                                    fontSize: '0.875rem',
                                  }}
                                >
                                  {call.status === 'completed' ? 'No summary available' : '—'}
                                </Typography>
                              )}
                              {call.summary && call.summary.length > 100 && (
                                <Typography
                                  variant='caption'
                                  sx={{
                                    cursor: 'pointer',
                                    color: '#1976d2',
                                    fontSize: '0.75rem',
                                  }}
                                >
                                  Read more...
                                </Typography>
                              )}
                            </Box>
                          </TableCell>

                          <TableCell sx={{ py: 2 }}>
                            <Tooltip title={call.remarks ? 'View/Edit remark' : 'Add remark'}>
                              <Button
                                size='small'
                                variant='outlined'
                                startIcon={
                                  call.remarks ? (
                                    <EditNoteIcon sx={{ fontSize: '14px' }} />
                                  ) : (
                                    <NoteAddIcon sx={{ fontSize: '14px' }} />
                                  )
                                }
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemarkModalOpen(call, e);
                                }}
                                sx={{
                                  textTransform: 'none',
                                  fontSize: '0.75rem',
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: '120px',
                                  borderColor: 'grey.300',
                                  color: 'text.primary',
                                  '&:hover': {
                                    borderColor: '#1976d2',
                                    backgroundColor: '#e3f2fd',
                                  },
                                }}
                              >
                                {call.remarks ? 'View Remark' : 'Add Remark'}
                              </Button>
                            </Tooltip>
                          </TableCell>
                          {/* Call ID Column */}

                          {/* Actions Column */}
                          <TableCell sx={{ py: 2 }}>
                            <Tooltip title='More actions'>
                              <IconButton
                                size='small'
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleMenuOpen(e, call);
                                }}
                                sx={{
                                  color: 'grey.600',
                                  '&:hover': {
                                    backgroundColor: '#f5f5f5',
                                    color: 'text.primary',
                                  },
                                }}
                              >
                                <MoreVertIcon sx={{ fontSize: '18px' }} />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                        {/* Enhanced Expanded Row */}
                        <TableRow>
                          <TableCell
                            style={{ paddingBottom: 0, paddingTop: 0, border: 0 }}
                            colSpan={9}
                          >
                            <Collapse in={expandedRow === call.id} timeout='auto' unmountOnExit>
                              <Box
                                sx={{
                                  margin: 2,
                                  backgroundColor: '#fafafa',
                                  borderRadius: 2,
                                  p: 3,
                                  border: '1px solid',
                                  borderColor: 'grey.200',
                                }}
                              >
                                {/* Call Details Section - Single Line */}
                                <Box sx={{ mb: 3 }}>
                                  <Typography
                                    variant='subtitle2'
                                    sx={{
                                      fontWeight: 600,
                                      color: 'text.primary',
                                      mb: 1,
                                      fontSize: '0.875rem',
                                    }}
                                  >
                                    Call Details:
                                  </Typography>
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      flexWrap: 'wrap',
                                      gap: 3,
                                      alignItems: 'center',
                                      fontSize: '0.875rem',
                                    }}
                                  >
                                    <Typography variant='body2' sx={{ color: 'text.secondary' }}>
                                      <strong>Call ID:</strong>{' '}
                                      {call.unique_id || `CALL-${call.id}`}
                                    </Typography>
                                    <Typography variant='body2' sx={{ color: 'text.secondary' }}>
                                      <strong>From:</strong> {call.from_number || 'N/A'}
                                    </Typography>
                                    <Typography variant='body2' sx={{ color: 'text.secondary' }}>
                                      <strong>Type:</strong> {call.call_type || 'Outbound'}
                                    </Typography>
                                    <Typography variant='body2' sx={{ color: 'text.secondary' }}>
                                      <strong>Persona:</strong>{' '}
                                      {detailedCalls[call.id]?.persona?.name ||
                                        call.persona_name ||
                                        'NPS'}{' '}
                                      •
                                      <span style={{ marginLeft: 4 }}>
                                        Engine:{' '}
                                        {detailedCalls[call.id]?.persona?.version?.engine ||
                                          'Custom'}
                                      </span>{' '}
                                      •
                                      <span style={{ marginLeft: 4 }}>
                                        Voice:{' '}
                                        {detailedCalls[call.id]?.persona?.version?.voice ||
                                          'Zephyr'}
                                      </span>{' '}
                                      •
                                      <span style={{ marginLeft: 4 }}>
                                        Language:{' '}
                                        {detailedCalls[call.id]?.persona?.version?.language ||
                                          'Hindi'}
                                      </span>
                                    </Typography>
                                  </Box>
                                </Box>

                                {/* Prompt Details Section - Single Line */}
                                <Box sx={{ mb: 3 }}>
                                  <Typography
                                    variant='subtitle2'
                                    sx={{
                                      fontWeight: 600,
                                      color: 'text.primary',
                                      mb: 1,
                                      fontSize: '0.875rem',
                                    }}
                                  >
                                    Prompt:
                                  </Typography>
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      flexWrap: 'wrap',
                                      gap: 3,
                                      alignItems: 'center',
                                      fontSize: '0.875rem',
                                    }}
                                  >
                                    <Typography variant='body2' sx={{ color: 'text.secondary' }}>
                                      <strong>
                                        {detailedCalls[call.id]?.prompt?.name || 'Conversational'}
                                      </strong>{' '}
                                      •
                                      <span style={{ marginLeft: 4 }}>
                                        Model:{' '}
                                        {detailedCalls[call.id]?.prompt?.version?.model || 'gpt-4'}
                                      </span>{' '}
                                      •
                                      <span style={{ marginLeft: 4 }}>
                                        Version:{' '}
                                        {detailedCalls[call.id]?.prompt?.version?.version || '1.2'}
                                      </span>
                                    </Typography>
                                  </Box>
                                </Box>

                                {/* Conversation Responses Section - Horizontal Scrollable */}
                                {detailedCalls[call.id]?.responses &&
                                detailedCalls[call.id].responses.length > 0 ? (
                                  <Box>
                                    <Typography
                                      variant='subtitle2'
                                      sx={{
                                        fontWeight: 600,
                                        color: 'text.primary',
                                        mb: 2,
                                        fontSize: '0.875rem',
                                      }}
                                    >
                                      Conversation Responses
                                    </Typography>

                                    {/* Horizontal Scrollable Container */}
                                    <Box
                                      sx={{
                                        display: 'flex',
                                        gap: 2,
                                        overflowX: 'auto',
                                        pb: 1,
                                        '&::-webkit-scrollbar': {
                                          height: 6,
                                        },
                                        '&::-webkit-scrollbar-track': {
                                          backgroundColor: '#f1f1f1',
                                          borderRadius: 3,
                                        },
                                        '&::-webkit-scrollbar-thumb': {
                                          backgroundColor: '#c1c1c1',
                                          borderRadius: 3,
                                          '&:hover': {
                                            backgroundColor: '#a8a8a8',
                                          },
                                        },
                                      }}
                                    >
                                      {detailedCalls[call.id].responses.map((response, index) => (
                                        <Card
                                          key={index}
                                          variant='outlined'
                                          sx={{
                                            minWidth: 280,
                                            maxWidth: 320,
                                            flexShrink: 0,
                                            backgroundColor: 'white',
                                            border: '1px solid',
                                            borderColor: 'grey.300',
                                            borderRadius: 2,
                                            boxShadow: '0 1px 3px rgba(0,0,0,0.08)',
                                          }}
                                        >
                                          <CardContent sx={{ p: 2.5, '&:last-child': { pb: 2.5 } }}>
                                            {/* Question Label */}
                                            <Typography
                                              variant='caption'
                                              sx={{
                                                color: 'text.secondary',
                                                fontWeight: 600,
                                                textTransform: 'uppercase',
                                                letterSpacing: 0.5,
                                                mb: 1,
                                                display: 'block',
                                                fontSize: '0.7rem',
                                              }}
                                            >
                                              Question {index + 1}
                                            </Typography>

                                            {/* Question Text */}
                                            <Typography
                                              variant='body2'
                                              sx={{
                                                fontWeight: 500,
                                                color: 'text.primary',
                                                mb: 2,
                                                lineHeight: 1.4,
                                                fontSize: '0.875rem',
                                              }}
                                            >
                                              {response.question_text}
                                            </Typography>

                                            {/* Answer Label */}
                                            <Typography
                                              variant='caption'
                                              sx={{
                                                color: 'text.secondary',
                                                fontWeight: 600,
                                                textTransform: 'uppercase',
                                                letterSpacing: 0.5,
                                                mb: 0.5,
                                                display: 'block',
                                                fontSize: '0.7rem',
                                              }}
                                            >
                                              Answer
                                            </Typography>

                                            {/* Answer Text */}
                                            <Typography
                                              variant='body2'
                                              sx={{
                                                color: response.answer_text
                                                  ? 'text.primary'
                                                  : 'text.secondary',
                                                fontStyle: response.answer_text
                                                  ? 'normal'
                                                  : 'italic',
                                                lineHeight: 1.4,
                                                fontSize: '0.875rem',
                                              }}
                                            >
                                              {response.answer_text || 'No response recorded'}
                                            </Typography>
                                          </CardContent>
                                        </Card>
                                      ))}
                                    </Box>
                                  </Box>
                                ) : null}
                              </Box>
                            </Collapse>
                          </TableCell>
                        </TableRow>
                      </React.Fragment>
                    ))
                )}
              </TableBody>
            </Table>
            <Box
              sx={{
                borderTop: '1px solid',
                borderColor: 'grey.200',
                backgroundColor: '#fafafa',
              }}
            >
              <TablePagination
                rowsPerPageOptions={[10, 25, 50]}
                component='div'
                count={totalCount}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                sx={{
                  '& .MuiTablePagination-toolbar': {
                    px: 3,
                    py: 1,
                  },
                  '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
                    fontSize: '0.875rem',
                    color: 'text.secondary',
                  },
                }}
              />
            </Box>
          </TableContainer>
        </Paper>

        {/* Call Details Dialog */}
        <Dialog open={detailsOpen} onClose={() => setDetailsOpen(false)} maxWidth='md' fullWidth>
          <DialogTitle>
            Call Details
            <IconButton
              aria-label='close'
              onClick={() => setDetailsOpen(false)}
              sx={{ position: 'absolute', right: 8, top: 8 }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            {selectedCall && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Typography variant='subtitle2' gutterBottom>
                    Call Information
                  </Typography>
                  <Typography variant='body2'>
                    <strong>ID:</strong> {selectedCall.id}
                  </Typography>
                  <Typography variant='body2'>
                    <strong>From:</strong> {selectedCall.from_number}
                  </Typography>
                  <Typography variant='body2'>
                    <strong>To:</strong> {selectedCall.to_number}
                  </Typography>
                  <Typography variant='body2'>
                    <strong>Type:</strong> {selectedCall.call_type || 'Outbound'}
                  </Typography>
                  <Typography variant='body2'>
                    <strong>Duration:</strong> {formatDuration(selectedCall.duration_seconds)}
                  </Typography>
                  <Typography variant='body2'>
                    <strong>Date/Time:</strong> {new Date(selectedCall.created_at).toLocaleString()}
                  </Typography>
                  <Typography variant='body2'>
                    <strong>Status:</strong>{' '}
                    <Chip
                      label={formatStatusLabel(selectedCall.status)}
                      size='small'
                      sx={{
                        backgroundColor: getStatusColor(selectedCall.status),
                        color: getStatusTextColor(selectedCall.status),
                        fontWeight: 500,
                        fontSize: '0.75rem',
                      }}
                    />
                  </Typography>
                  {selectedCall.failure_reason && (
                    <Typography variant='body2' color='error' sx={{ mt: 1 }}>
                      <strong>Error:</strong> {selectedCall.failure_reason}
                    </Typography>
                  )}
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant='subtitle2' gutterBottom>
                    Variables
                  </Typography>
                  <Paper variant='outlined' sx={{ p: 2 }}>
                    {selectedCall.variables ? (
                      Object.entries(selectedCall.variables).map(([key, value]) => (
                        <Typography key={key} variant='body2'>
                          <strong>{key}:</strong> {value}
                        </Typography>
                      ))
                    ) : (
                      <Typography variant='body2' color='text.secondary'>
                        No variables set
                      </Typography>
                    )}
                  </Paper>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDetailsOpen(false)}>Close</Button>
          </DialogActions>
        </Dialog>

        {/* Audio Player Modal */}
        <Dialog
          open={audioModalOpen}
          onClose={() => {
            setAudioModalOpen(false);
            if (audioRef.current) {
              audioRef.current.pause();
              setIsPlaying(false);
            }
          }}
          maxWidth='sm'
          fullWidth
        >
          <DialogTitle>
            Call Recording
            <IconButton
              aria-label='close'
              onClick={() => {
                setAudioModalOpen(false);
                if (audioRef.current) {
                  audioRef.current.pause();
                  setIsPlaying(false);
                }
              }}
              sx={{ position: 'absolute', right: 8, top: 8 }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent>
            <Box sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <IconButton
                  onClick={handlePlayPause}
                  size='large'
                  disabled={!selectedAudio?.url || isAudioLoading}
                  sx={{
                    color: 'success.main',
                    '&:hover': {
                      backgroundColor: 'success.light',
                      color: 'success.contrastText',
                    },
                  }}
                >
                  {isAudioLoading ? (
                    <CircularProgress size={24} color='success' />
                  ) : isPlaying ? (
                    <PauseIcon />
                  ) : (
                    <PlayArrowIcon />
                  )}
                </IconButton>
                <Box sx={{ flex: 1, mx: 2 }}>
                  <Slider
                    value={currentTime}
                    max={duration || 100}
                    onChange={handleSliderChange}
                    aria-labelledby='audio-slider'
                    disabled={!selectedAudio?.url || isAudioLoading}
                    sx={{
                      color: 'success.main',
                      '& .MuiSlider-thumb': {
                        '&:hover, &.Mui-focusVisible': {
                          boxShadow: '0px 0px 0px 8px rgba(76, 175, 80, 0.16)',
                        },
                      },
                    }}
                  />
                </Box>
                <Typography variant='body2' sx={{ minWidth: 100 }}>
                  {formatTime(currentTime)} / {formatTime(duration)}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  startIcon={<DownloadIcon />}
                  onClick={handleDownload}
                  variant='outlined'
                  size='small'
                  disabled={!selectedAudio?.url || isAudioLoading}
                  sx={{
                    color: 'success.main',
                    borderColor: 'success.main',
                    '&:hover': {
                      borderColor: 'success.dark',
                      backgroundColor: 'success.light',
                      color: 'success.dark',
                    },
                  }}
                >
                  Download Recording
                </Button>
              </Box>
            </Box>
          </DialogContent>
        </Dialog>

        {/* Action Menu */}
        <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
          {selectedCall?.status === 'completed' && (
            <>
              <MenuItem
                onClick={() => {
                  setSelectedCall(detailedCalls[selectedCall.id] || selectedCall);
                  setDetailsOpen(true);
                  handleMenuClose();
                }}
              >
                <InfoIcon fontSize='small' sx={{ mr: 1 }} />
                View More Info
              </MenuItem>
              <MenuItem
                onClick={() => {
                  setSelectedCall(detailedCalls[selectedCall.id] || selectedCall);
                  // setResponsesOpen(true);
                  handleMenuClose();
                }}
              >
                <DescriptionIcon fontSize='small' sx={{ mr: 1 }} />
                View Responses
              </MenuItem>
            </>
          )}
          {selectedCall?.status === 'failed' && (
            <MenuItem
              onClick={() => {
                handleRetryCall(selectedCall.id);
                handleMenuClose();
              }}
            >
              <RefreshIcon fontSize='small' sx={{ mr: 1 }} />
              Retry Call
            </MenuItem>
          )}
        </Menu>
      </Container>

      {/* Remark Dialog */}
      <Dialog open={remarkModalOpen} onClose={handleRemarkModalClose} maxWidth='sm' fullWidth>
        <DialogTitle>
          {selectedCallForRemark?.remarks ? 'View/Edit Remark' : 'Add Remark'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Call ID:{' '}
            {selectedCallForRemark?.request_id ||
              selectedCallForRemark?.unique_id ||
              `CALL-${selectedCallForRemark?.id}`}
          </DialogContentText>
          <TextareaAutosize
            minRows={5}
            style={{
              width: '100%',
              padding: '8px',
              fontFamily: 'inherit',
              fontSize: '0.875rem',
              borderRadius: '4px',
              border: '1px solid rgba(0, 0, 0, 0.23)',
            }}
            value={currentRemark}
            onChange={(e) => setCurrentRemark(e.target.value)}
            placeholder='Add your remarks here...'
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleRemarkModalClose} disabled={isSavingRemark}>
            Cancel
          </Button>
          <Button
            onClick={handleSaveRemark}
            variant='contained'
            disabled={isSavingRemark || currentRemark === (selectedCallForRemark?.remarks || '')}
          >
            {isSavingRemark ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Saving...
              </>
            ) : (
              'Save'
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default CallLogs;

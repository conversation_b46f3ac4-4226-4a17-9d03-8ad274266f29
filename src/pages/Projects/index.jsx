import React, { useState, useEffect, useCallback, useRef } from 'react';
import DataTable from '../../components/DataTable/DataTable';
import { listProjects, createProject, updateProject } from '../../services/projectsService';
import ProjectFormModal from './ProjectFormModal';
import { useNavigate } from 'react-router-dom';
import useNotify from '../../hooks/useNotify';
import { TextField, InputAdornment, Box, Typography } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import ArchiveIcon from '@mui/icons-material/Archive';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

const columnsBase = [
  { headerName: 'Title', field: 'title' },
  { headerName: 'Language', field: 'language' },
  { headerName: 'Region', field: 'region' },
  { headerName: 'Status', field: 'status' },
];

const Projects = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const notify = useNotify();

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create' or 'edit'
  const [modalInitialValues, setModalInitialValues] = useState({
    title: '',
    language: '',
    region: '',
    status: 'Active',
  });
  const [actionLoading, setActionLoading] = useState(false);
  const [actionError, setActionError] = useState(null);

  const fetchProjects = useCallback(async (currentPage = 0, currentLimit = 10) => {
    setLoading(true);
    setError(null);
    try {
      const res = await listProjects({
        skip: currentPage * currentLimit,
        limit: currentLimit,
      });
      setProjects(res.items || []);
      setTotalCount(res.total ?? 0);
    } catch (err) {
      console.error('Error loading projects:', err);
      setError('Failed to load projects');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProjects(page, rowsPerPage);
  }, [fetchProjects, page, rowsPerPage]);

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // CREATE
  const handleOpenCreate = () => {
    setModalMode('create');
    setModalInitialValues({ title: '', language: '', region: '', status: 'Active' });
    setModalOpen(true);
    setActionError(null);
  };

  // EDIT
  const handleOpenEdit = (project, e) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    console.log(project);
    setModalOpen(true);
    setModalMode('edit');
    setModalInitialValues({ ...project });
    setActionError(null);
  };

  // DELETE
  const handleDelete = async (projectId, e) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    if (!window.confirm('Are you sure you want to Archive this project?')) return;
    setActionLoading(true);
    setActionError(null);
    try {
      await updateProject(projectId, { status: 'Archived' });
      fetchProjects(page, rowsPerPage);
    } catch (err) {
      setActionError('Failed to delete project');
    } finally {
      setActionLoading(false);
    }
  };

  // ROW CLICK - Navigate to project detail
  const handleRowClick = (row) => {
    if (row && row.id) {
      navigate(`/projects/${row.id}`);
    }
  };

  // SUBMIT (CREATE/EDIT)
  const handleModalSubmit = async (values) => {
    setActionLoading(true);
    setActionError(null);
    try {
      if (modalMode === 'edit') {
        await updateProject(values.id, values);
      } else {
        await createProject(values);
      }
      setModalOpen(false);
      fetchProjects(page, rowsPerPage);
      notify(`Project saved successfully!`);
    } catch (err) {
      notify(err.message, 'error');
      setActionError('Failed to save project');
    } finally {
      console.log('Project saved successfully!');
      setActionLoading(false);
    }
  };

  // Columns with actions
  const columns = [
    ...columnsBase,
    {
      headerName: 'Actions',
      field: 'actions',
      renderCell: (params) => {
        return (
          <div style={{ display: 'flex', gap: '8px' }}>
            <Tooltip title='Edit Project'>
              <IconButton
                size='small'
                color='primary'
                onClick={(e) => {
                  e.stopPropagation();
                  handleOpenEdit(params.row);
                }}
              >
                <EditIcon fontSize='small' />
              </IconButton>
            </Tooltip>
            <Tooltip title='Archive Project'>
              <IconButton
                size='small'
                color='error'
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(params.row.id, e);
                }}
              >
                <ArchiveIcon fontSize='small' />
              </IconButton>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  // State for search query
  const [searchQuery, setSearchQuery] = useState('');

  // Function to handle search
  // Debounce search to avoid excessive API calls
  const searchTimeout = useRef(null);

  const handleSearch = (query) => {
    setSearchQuery(query);

    // Clear any existing timeout
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    // Set a new timeout to delay the search
    searchTimeout.current = setTimeout(async () => {
      setLoading(true);
      try {
        const res = await listProjects({ skip: 0, limit: rowsPerPage, search: query });
        setProjects(res.items || []);
        setTotalCount(res.total ?? 0);
        setPage(0);
      } catch (err) {
        setError('Failed to search projects');
      } finally {
        setLoading(false);
      }
    }, 1000); // 300ms debounce time
  };

  // Clean up the timeout on component unmount
  useEffect(() => {
    return () => {
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }
    };
  }, []);

  // Custom header component with Create Project button and search
  const TableHeader = () => (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        width: '100%',
        alignItems: 'center',
      }}
    >
      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-' }}>
        <TextField
          variant='outlined'
          placeholder='Search projects...'
          size='small'
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position='start'>
                <SearchIcon color='action' />
              </InputAdornment>
            ),
          }}
          sx={{
            minWidth: 250,
            '& .MuiOutlinedInput-root': {
              borderRadius: '8px',
            },
          }}
        />
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-between',
            mb: 2,
          }}
        >
          <IconButton
            color='primary'
            onClick={handleOpenCreate}
            sx={{
              backgroundColor: '#1976d2',
              color: 'white',
              borderRadius: '8px',
              padding: '8px 16px',
              '&:hover': {
                backgroundColor: '#1565c0',
              },
            }}
          >
            <AddIcon sx={{ mr: 1 }} />
            <Typography variant='button' sx={{ textTransform: 'none' }}>
              Create Project
            </Typography>
          </IconButton>
        </Box>
      </Box>
    </Box>
  );

  return (
    <div>
      {error && <div style={{ color: 'red', marginBottom: 16 }}>{error}</div>}
      {actionError && <div style={{ color: 'red', marginBottom: 16 }}>{actionError}</div>}
      <DataTable
        columns={columns}
        data={projects}
        totalCount={totalCount}
        loading={loading || actionLoading}
        onPageChange={handlePageChange}
        onRowsPerPageChange={(newRowsPerPage) => {
          handleRowsPerPageChange({ target: { value: newRowsPerPage } });
        }}
        serverSidePagination={true}
        title={<TableHeader />} // Use custom header component
        searchEnabled={false} // Disable the built-in search
        emptyStateMessage={loading ? 'Loading...' : 'No projects found'}
        onRowClick={handleRowClick}
      />
      <ProjectFormModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSubmit={handleModalSubmit}
        initialValues={modalInitialValues}
        mode={modalMode}
      />
    </div>
  );
};
export default Projects;

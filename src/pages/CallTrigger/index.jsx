import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  CircularProgress,
  useTheme,
  Divider,
  Alert,
  Grid,
  Card,
  CardContent,
  Stepper,
  Step,
  StepLabel,
  IconButton,
  Tooltip,
  InputAdornment,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Switch,
  FormControlLabel,
  Radio,
  RadioGroup,
  Container,
} from '@mui/material';
import {
  Phone as PhoneIcon,
  Send as SendIcon,
  Info as InfoIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Message as MessageIcon,
  Help as HelpIcon,
  Language as LanguageIcon,
  RecordVoiceOver as VoiceIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useProject } from '../../contexts/ProjectContext';
import { listPersonas } from '../../services/personaService';
import { listPrompts } from '../../services/promptService';
import { triggerCall } from '../../services/callService';

const CallTrigger = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { selectedProject } = useProject();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [personas, setPersonas] = useState([]);
  const [selectedPersona, setSelectedPersona] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [variables, setVariables] = useState({});
  const [promptText, setPromptText] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeStep, setActiveStep] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [selectedPersonaVersion, setSelectedPersonaVersion] = useState(null);

  const steps = ['Choose Persona', 'Enter Details', 'Review & Trigger'];

  const fetchPersonas = useCallback(async () => {
    try {
      const params = {};
      if (selectedProject?.id) {
        params.project_id = selectedProject.id;
      }
      const res = await listPersonas(params);
      // Sort personas to put default first
      const sortedPersonas = (res.items || []).sort((a, b) => {
        if (a.is_default && !b.is_default) return -1;
        if (!a.is_default && b.is_default) return 1;
        return 0;
      });
      setPersonas(sortedPersonas);

      // Select default persona if available
      const defaultPersona = sortedPersonas.find((p) => p.is_default);
      if (defaultPersona) {
        setSelectedPersona(defaultPersona.id);
        await fetchPrompt(defaultPersona.id);
      }
    } catch (err) {
      console.error('Error loading personas:', err);
      setError('Failed to load personas');
    }
  }, [selectedProject]);

  const fetchPrompt = async (personaId) => {
    try {
      const params = {
        persona_id: personaId,
        type: 'conversational',
      };
      const res = await listPrompts(params);
      if (res.items && res.items.length > 0) {
        const prompt = res.items[0];
        setPromptText(prompt.current_version?.prompt_text || '');
        const initialVariables = {};
        if (prompt.current_version?.variables) {
          prompt.current_version.variables.forEach((variable) => {
            initialVariables[variable] = '';
          });
        }
        setVariables(initialVariables);
      }
    } catch (err) {
      console.error('Error loading prompt:', err);
      setError('Failed to load prompt');
    }
  };

  useEffect(() => {
    fetchPersonas();
  }, [fetchPersonas]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleVariableChange = (variable, value) => {
    setVariables((prev) => ({
      ...prev,
      [variable]: value,
    }));
  };

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Validate phone number
      const phoneRegex = /^[0-9]{10}$/;
      if (!phoneRegex.test(phoneNumber)) {
        throw new Error('Please enter a valid 10-digit phone number');
      }

      const selectedPersonaData = personas.find((p) => p.id === selectedPersona);
      if (!selectedPersonaData || !selectedPersonaData.current_version) {
        throw new Error('No persona version selected');
      }

      const payload = {
        project_id: selectedProject.id,
        persona_version_id: selectedPersonaData.current_version.id,
        phone_number: phoneNumber,
        variables: variables,
      };

      await triggerCall(payload);
      setSuccess('Call triggered successfully!');

      // Redirect to call logs after a short delay to show success message
      setTimeout(() => {
        navigate('/call-logs');
      }, 1500);
    } catch (err) {
      console.error('Error triggering call:', err);
      setError(err.message || err.response?.data?.message || 'Failed to trigger call');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Card variant='outlined' sx={{ mb: 2, flexGrow: 1 }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant='h6'>Choose Persona</Typography>
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {personas.map((persona) => (
                  <Paper
                    key={persona.id}
                    elevation={selectedPersona === persona.id ? 3 : 1}
                    sx={{
                      p: 2,
                      cursor: 'pointer',
                      borderRadius: 2,
                      border: '2px solid',
                      borderColor: selectedPersona === persona.id ? 'primary.main' : 'transparent',
                      backgroundColor:
                        selectedPersona === persona.id ? 'primary.50' : 'background.paper',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        borderColor: 'primary.main',
                        backgroundColor: 'primary.25',
                        transform: 'translateY(-1px)',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                      },
                    }}
                    onClick={async () => {
                      setSelectedPersona(persona.id);
                      await fetchPrompt(persona.id);
                      handleNext();
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar
                        sx={{
                          backgroundColor:
                            selectedPersona === persona.id ? 'primary.main' : 'grey.300',
                          color: 'white',
                        }}
                      >
                        <VoiceIcon />
                      </Avatar>

                      <Box sx={{ flex: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <Typography variant='h6' sx={{ fontWeight: 600 }}>
                            {persona.name}
                          </Typography>
                          {persona.is_default && (
                            <Chip
                              label='Default'
                              size='small'
                              color='primary'
                              variant='filled'
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                          )}
                        </Box>

                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                          <Chip
                            icon={<LanguageIcon />}
                            label={persona.current_version?.language || persona.language}
                            size='small'
                            variant='outlined'
                            sx={{ fontSize: '0.75rem' }}
                          />
                          <Chip
                            icon={<VoiceIcon />}
                            label={persona.current_version?.voice_type || persona.voice_type}
                            size='small'
                            variant='outlined'
                            sx={{ fontSize: '0.75rem' }}
                          />
                        </Box>
                      </Box>
                    </Box>
                  </Paper>
                ))}
              </Box>
            </CardContent>
          </Card>
        );

      case 1:
        return (
          <Card variant='outlined' sx={{ mb: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant='h6'>Enter Details</Typography>
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <TextField
                  fullWidth
                  label='Phone Number'
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value.replace(/[^0-9]/g, ''))}
                  required
                  error={phoneNumber.length > 0 && phoneNumber.length !== 10}
                  helperText={
                    phoneNumber.length > 0 && phoneNumber.length !== 10
                      ? 'Please enter a valid 10-digit phone number'
                      : 'Enter a 10-digit phone number'
                  }
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position='start'>
                        <PhoneIcon color='action' />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                    },
                  }}
                />

                {Object.keys(variables).length > 0 && (
                  <>
                    <Divider sx={{ my: 1 }}>
                      <Chip
                        label='Variables'
                        size='small'
                        sx={{
                          backgroundColor: 'primary.50',
                          color: 'primary.main',
                          fontWeight: 500,
                        }}
                      />
                    </Divider>

                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      {Object.keys(variables).map((variable) => (
                        <TextField
                          key={variable}
                          fullWidth
                          label={variable
                            .replace(/_/g, ' ')
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                          value={variables[variable]}
                          onChange={(e) => handleVariableChange(variable, e.target.value)}
                          required
                          helperText={`Enter the ${variable.replace(/_/g, ' ')}`}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 2,
                            },
                          }}
                        />
                      ))}
                    </Box>
                  </>
                )}
              </Box>
            </CardContent>
          </Card>
        );

      case 2:
        return (
          <Card variant='outlined' sx={{ mb: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <InfoIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant='h6'>Review & Confirm</Typography>
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2.5 }}>
                {/* Call Details Summary */}
                <Paper sx={{ p: 2.5, backgroundColor: 'grey.50', borderRadius: 2 }}>
                  <Typography variant='subtitle1' sx={{ fontWeight: 600, mb: 2 }}>
                    Call Details
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                        <Typography
                          variant='caption'
                          color='text.secondary'
                          sx={{ fontWeight: 500 }}
                        >
                          PROJECT
                        </Typography>
                        <Typography variant='body1' sx={{ fontWeight: 500 }}>
                          {selectedProject?.title}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                        <Typography
                          variant='caption'
                          color='text.secondary'
                          sx={{ fontWeight: 500 }}
                        >
                          PERSONA
                        </Typography>
                        <Typography variant='body1' sx={{ fontWeight: 500 }}>
                          {personas.find((p) => p.id === selectedPersona)?.name}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                        <Typography
                          variant='caption'
                          color='text.secondary'
                          sx={{ fontWeight: 500 }}
                        >
                          PHONE NUMBER
                        </Typography>
                        <Typography variant='body1' sx={{ fontWeight: 500 }}>
                          {phoneNumber}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Variables */}
                {Object.keys(variables).length > 0 && (
                  <Paper sx={{ p: 2.5, backgroundColor: 'primary.50', borderRadius: 2 }}>
                    <Typography variant='subtitle1' sx={{ fontWeight: 600, mb: 2 }}>
                      Variables
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {Object.entries(variables).map(([key, value]) => (
                        <Box
                          key={key}
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            py: 0.5,
                          }}
                        >
                          <Typography variant='body2' sx={{ fontWeight: 500 }}>
                            {key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}:
                          </Typography>
                          <Typography variant='body2' color='text.secondary'>
                            {value}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </Paper>
                )}

                {/* Prompt Preview */}
                <Box>
                  <Typography variant='subtitle1' sx={{ fontWeight: 600, mb: 1.5 }}>
                    Prompt Preview
                  </Typography>
                  <Paper
                    sx={{
                      p: 2.5,
                      backgroundColor: 'background.paper',
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 2,
                      maxHeight: 180,
                      overflow: 'auto',
                    }}
                  >
                    <Typography
                      variant='body2'
                      color='text.secondary'
                      sx={{ lineHeight: 1.6, whiteSpace: 'pre-wrap' }}
                    >
                      {promptText || 'Loading prompt...'}
                    </Typography>
                  </Paper>
                </Box>
              </Box>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ width: '100%', py: 3, px: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant='body1' color='text.secondary'>
          Trigger individual or bulk calls with selected personas
        </Typography>
      </Box>

      {/* Tabs Section */}
      <Paper sx={{ mb: 3, borderRadius: 2, overflow: 'hidden' }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '1rem',
              py: 2,
            },
          }}
        >
          <Tab label='Individual Trigger' />
          <Tab label='Bulk Trigger' />
        </Tabs>
      </Paper>

      {activeTab === 0 && (
        <Paper sx={{ p: 4, borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
          {/* Alert Messages */}
          {error && (
            <Alert
              severity='error'
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-message': {
                  fontSize: '0.95rem',
                },
              }}
            >
              {error}
            </Alert>
          )}
          {success && (
            <Alert
              severity='success'
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-message': {
                  fontSize: '0.95rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                },
              }}
              icon={<CheckCircleIcon />}
            >
              {success} Redirecting to Call Logs...
            </Alert>
          )}

          {/* Stepper */}
          <Stepper
            activeStep={activeStep}
            sx={{
              mb: 4,
              '& .MuiStepLabel-label': {
                fontSize: '0.95rem',
                fontWeight: 500,
              },
            }}
          >
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Step Content */}
          <Box sx={{ minHeight: 300 }}>{renderStepContent(activeStep)}</Box>

          {/* Navigation Buttons */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              mt: 3,
              pt: 3,
              borderTop: '1px solid',
              borderColor: 'divider',
            }}
          >
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
              variant='outlined'
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                px: 3,
                py: 1.5,
              }}
            >
              Back
            </Button>

            {activeStep === steps.length - 1 ? (
              <Button
                onClick={handleSubmit}
                variant='contained'
                color='primary'
                size='large'
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  px: 4,
                  py: 1.5,
                  fontWeight: 600,
                  boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                  '&:hover': {
                    boxShadow: '0 6px 16px rgba(25, 118, 210, 0.4)',
                  },
                }}
              >
                {loading ? 'Triggering Call...' : 'Trigger Call'}
              </Button>
            ) : (
              <Button
                variant='contained'
                onClick={handleNext}
                disabled={
                  (activeStep === 0 && !selectedPersona) ||
                  (activeStep === 1 && (!phoneNumber || Object.values(variables).some((v) => !v)))
                }
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  px: 3,
                  py: 1.5,
                  fontWeight: 500,
                }}
              >
                Next
              </Button>
            )}
          </Box>
        </Paper>
      )}

      {activeTab === 1 && (
        <Paper sx={{ p: 6, textAlign: 'center', borderRadius: 2 }}>
          <Typography variant='h6' color='text.secondary' sx={{ mb: 2 }}>
            Bulk Trigger functionality coming soon...
          </Typography>
          <Typography variant='body2' color='text.secondary'>
            This feature will allow you to trigger multiple calls at once using CSV uploads.
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default CallTrigger;

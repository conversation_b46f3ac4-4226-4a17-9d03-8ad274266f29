import React, { useEffect, useState } from 'react';
import DataTable from '../../components/DataTable/DataTable';
import {
  getUsers,
  getRoles,
  createUser,
  deleteUser,
  getUserById,
  updateUser,
} from '../../services/users';
import {
  Box,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
// import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { isGlobalAdmin } from '../../utils/user';
import { notifyUser } from '../../utils';
import UserForm from './UserForm';

const UserListing = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [roles, setRoles] = useState({});
  const [error, setError] = useState(null);
  const [userFormOpen, setUserFormOpen] = useState(false);
  const [userFormInitialValues, setUserFormInitialValues] = useState({});
  const [userFormEditMode, setUserFormEditMode] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  const globalAdmin = isGlobalAdmin();

  const fetchUsers = async ({ page, rowsPerPage }) => {
    setLoading(true);
    try {
      const response = (await getUsers({ skip: page * rowsPerPage, limit: rowsPerPage })) || {};
      setUsers(response.users || []);
      setTotalCount(response.total || 0);

      const rolesResponse = (await getRoles()) || {};
      setRoles(rolesResponse || {});
      setError(null);
    } catch (err) {
      setError('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers({ page, rowsPerPage });
  }, [page, rowsPerPage]);

  const columns = [
    { field: 'id', headerName: 'ID', flex: 1 },
    { field: 'username', headerName: 'User Name', flex: 2 },
    { field: 'email', headerName: 'Email', flex: 2 },
    {
      field: 'is_active',
      headerName: 'Active',
      flex: 2,
      renderCell: (params) => (
        <span
          style={{
            display: 'inline-block',
            padding: '2px 12px',
            borderRadius: 4,
            background: params.value ? '#e6f4ea' : '#fde8e8',
            color: params.value ? '#2e7d32' : '#c62828',
            fontWeight: 500,
            fontSize: 12,
          }}
        >
          {params.value ? 'Active' : 'Deactivated'}
        </span>
      ),
    },
    {
      field: 'roles',
      headerName: 'Roles',
      flex: 3,
      renderCell: (params) => (
        <Grid container display='flex' gap={1}>
          {params.value.map((role) => (
            <Grid
              item
              key={role}
              style={{
                padding: '2px 12px',
                borderRadius: 4,
                background: '#e3f2fd',
                color: '#1565c0',
                fontWeight: 500,
                fontSize: 12,
                whiteSpace: 'nowrap',
              }}
            >
              {roles[role]}
            </Grid>
          ))}
        </Grid>
      ),
    },
  ];

  if (globalAdmin) {
    columns.push({
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      sortable: false,
      filterable: false,
      align: 'center',
      renderCell: (params) => (
        <div style={{ display: 'flex', gap: 8 }}>
          <IconButton
            size='small'
            color='primary'
            onClick={async () => {
              try {
                getUserById(params.row.id).then((response) => {
                  setUserFormInitialValues(response);
                  setUserFormEditMode(true);
                  setUserFormOpen(true);
                });
              } catch (e) {
                setError('Failed to fetch user details');
                notifyUser('Failed to fetch user details ' + e.message, 'error');
              }
            }}
          >
            <EditIcon fontSize='small' />
          </IconButton>
          {/* <IconButton
            size='small'
            color='error'
            onClick={() => {
              setUserToDelete(params.row);
              setDeleteDialogOpen(true);
            }}
          >
            <DeleteIcon fontSize='small' />
          </IconButton> */}
        </div>
      ),
    });
  }

  return (
    <Box pt={0} px={2} pb={2} mt={-2}>
      {globalAdmin && (
        <Box display='flex' justifyContent='flex-end' mb={1}>
          <Button
            variant='contained'
            color='primary'
            startIcon={<AddIcon />}
            onClick={() => {
              setUserFormInitialValues({});
              setUserFormOpen(true);
            }}
          >
            Add User
          </Button>
        </Box>
      )}

      <DataTable
        columns={columns}
        data={users}
        loading={loading}
        error={error}
        headerEnabled={false}
        serverSidePagination
        onPageChange={(page) => setPage(page)}
        onRowsPerPageChange={(rowsPerPage) => {
          setRowsPerPage(rowsPerPage);
          setPage(0);
        }}
        totalCount={totalCount}
      />
      <UserForm
        open={userFormOpen}
        onClose={() => {
          setUserFormOpen(false);
          setUserFormEditMode(false);
          setUserFormInitialValues({});
        }}
        onSave={async (formData) => {
          try {
            if (userFormEditMode) {
              await updateUser(userFormInitialValues.id, formData);
              notifyUser('User updated successfully', 'success');
            } else {
              await createUser(formData);
              notifyUser('User created successfully', 'success');
            }
            setUserFormOpen(false);
            setUserFormEditMode(false);
            setUserFormInitialValues({});
            fetchUsers({ page, rowsPerPage });
          } catch (e) {
            setError(e.message);
            notifyUser('Failed to save user ' + e.message, 'error');
          }
        }}
        rolesList={roles}
        initialValues={userFormInitialValues}
        isEdit={userFormEditMode}
      />
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete User</DialogTitle>
        <DialogContent>Are you sure you want to delete this user?</DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            color='error'
            variant='contained'
            onClick={async () => {
              try {
                await deleteUser(userToDelete.id);
                setDeleteDialogOpen(false);
                setUserToDelete(null);
                fetchUsers({ page, rowsPerPage });
                notifyUser('User deleted successfully', 'success');
              } catch (e) {
                setError('Failed to delete user');
                notifyUser('Failed to delete user ' + e.message, 'error');
              }
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserListing;

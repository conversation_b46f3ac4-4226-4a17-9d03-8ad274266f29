import React, { useEffect, useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  MenuItem,
  Box,
  FormControlLabel,
  Checkbox,
} from '@mui/material';

import { listProjects } from '../../services/projectsService';
import { getAllUsers } from '../../services/users';

const UserProjectForm = ({ open, onClose, onSave, initialValues = {}, isEdit = false }) => {
  const [form, setForm] = useState({
    user_id: '',
    project_id: '',
    is_active: true,
    ...initialValues,
  });
  const [projects, setProjects] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    setForm({
      user_id: '',
      project_id: '',
      is_active: true,
      ...initialValues,
    });
  }, [initialValues, open]);

  useEffect(() => {
    async function fetchProjects() {
      try {
        setLoading(true);
        const res = await listProjects({ limit: 100 });
        setProjects(res.items || []);
      } finally {
        setLoading(false);
      }
    }
    async function fetchUsers() {
      try {
        setLoading(true);
        const res = await getAllUsers();
        setUsers(res || []);
      } finally {
        setLoading(false);
      }
    }
    fetchProjects();
    fetchUsers();
  }, []);

  const validate = () => {
    const errs = {};
    if (!form.user_id) errs.user_id = 'Required';
    if (!form.project_id) errs.project_id = 'Required';
    return errs;
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSubmit = (e) => {
    const errs = validate();
    setErrors(errs);
    if (Object.keys(errs).length === 0) {
      onSave(form);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth='xs' fullWidth>
      <DialogTitle>{isEdit ? 'Edit User Project' : 'Add User Project'}</DialogTitle>
      <DialogContent>
        <Box display='flex' flexDirection='column' gap={2} mt={1}>
          <TextField
            select
            fullWidth
            label='User Email'
            name='user_id'
            error={!!errors.user_id}
            helperText={errors.user_id}
            value={form.user_id}
            onChange={handleChange}
            required
          >
            {users.map((user) => (
              <MenuItem key={user.id} value={user.id}>
                {user.email}
              </MenuItem>
            ))}
          </TextField>
          <TextField
            select
            fullWidth
            label='Project'
            name='project_id'
            error={!!errors.project_id}
            helperText={errors.project_id}
            value={form.project_id}
            onChange={handleChange}
            required
          >
            {projects.map((proj) => (
              <MenuItem key={proj.id} value={proj.id}>
                {proj.title}
              </MenuItem>
            ))}
          </TextField>
          <FormControlLabel
            control={
              <Checkbox
                checked={form.is_active}
                onChange={handleChange}
                name='is_active'
                color='primary'
              />
            }
            label='Active'
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant='contained' disabled={loading}>
          {isEdit ? 'Update' : 'Add'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UserProjectForm;

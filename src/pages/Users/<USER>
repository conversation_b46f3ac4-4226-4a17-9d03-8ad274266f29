import React, { useState } from 'react';
import { Container, Paper, Box, Tabs, Tab } from '@mui/material';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import BusinessIcon from '@mui/icons-material/Business';
import UserListing from './UserListing';
import UserProjectsListing from './UserProjectsListing';

/**
 * Users page component
 * @returns {JSX.Element} Users page
 */
const UsersManagement = () => {
  const [tabIndex, setTabIndex] = useState(0);

  const handleTabChange = (_, newValue) => {
    setTabIndex(newValue);
  };

  return (
    <Container maxWidth='lg' sx={{ py: 2 }}>
      <Paper sx={{ p: 3, mt: 2 }}>
        <Tabs value={tabIndex} onChange={handleTabChange} aria-label='Users Tabs'>
          <Tab label='Users' icon={<PeopleAltIcon />} iconPosition='start' />
          <Tab label='User Projects' icon={<BusinessIcon />} iconPosition='start' />
        </Tabs>
        <Box mt={2}>
          {tabIndex === 0 && <UserListing />}
          {tabIndex === 1 && <UserProjectsListing />}
        </Box>
      </Paper>
    </Container>
  );
};

export default UsersManagement;

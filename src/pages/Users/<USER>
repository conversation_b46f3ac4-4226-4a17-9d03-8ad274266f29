import React, { useEffect, useState } from 'react';
import { Add, Edit, Delete } from '@mui/icons-material';
import { Button, Box, IconButton } from '@mui/material';

import DataTable from '../../components/DataTable/DataTable';
import {
  getUserProjects,
  deleteUserProject,
  createUserProject,
  updateUserProject,
  getUserProjectById,
} from '../../services/users';
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { notifyUser } from '../../utils';
import UserProjectForm from './UserProjectForm';

const UserProjectsListing = () => {
  const [userProjects, setUserProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState(null);
  const [projectFormOpen, setProjectFormOpen] = useState(false);
  const [projectFormEditMode, setProjectFormEditMode] = useState(false);
  const [projectFormInitialValues, setProjectFormInitialValues] = useState({});

  const fetchUserProjects = async ({ page, rowsPerPage }) => {
    setLoading(true);
    try {
      const response =
        (await getUserProjects({ skip: page * rowsPerPage, limit: rowsPerPage })) || {};
      setUserProjects(response.user_projects || []);
      setTotalCount(response.total || 0);
      setError(null);
    } catch (err) {
      setError('Failed to fetch user projects');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserProjects({ page, rowsPerPage });
  }, [page, rowsPerPage]);

  const columns = [
    { field: 'user_mail', headerName: 'User', flex: 2 },
    { field: 'project_name', headerName: 'Project', flex: 2 },
    {
      field: 'is_active',
      headerName: 'Active',
      flex: 2,
      renderCell: (params) => (
        <span
          style={{
            display: 'inline-block',
            padding: '2px 12px',
            borderRadius: 4,
            background: params.value ? '#e6f4ea' : '#fde8e8',
            color: params.value ? '#2e7d32' : '#c62828',
            fontWeight: 500,
            fontSize: 12,
          }}
        >
          {params.value ? 'Active' : 'Deactivated'}
        </span>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      sortable: false,
      filterable: false,
      align: 'center',
      renderCell: (params) => (
        <div style={{ display: 'flex', gap: 8 }}>
          <IconButton
            size='small'
            color='primary'
            onClick={async () => {
              try {
                const data = await getUserProjectById(params.row.id);
                setProjectFormInitialValues(data);
                setProjectFormEditMode(true);
                setProjectFormOpen(true);
              } catch (e) {
                setError('Failed to fetch user project details');
                notifyUser('Failed to fetch user project details ' + e.message, 'error');
              }
            }}
          >
            <Edit fontSize='small' />
          </IconButton>
          <IconButton
            size='small'
            color='error'
            onClick={() => {
              setProjectToDelete(params.row);
              setDeleteDialogOpen(true);
            }}
          >
            <Delete fontSize='small' />
          </IconButton>
        </div>
      ),
    },
  ];

  return (
    <Box pt={0} px={2} pb={2} mt={-2}>
      <Box display='flex' justifyContent='flex-end' mb={1}>
        <Button
          variant='contained'
          color='primary'
          startIcon={<Add fontSize='small' />}
          onClick={() => {
            setProjectFormInitialValues({});
            setProjectFormEditMode(false);
            setProjectFormOpen(true);
          }}
        >
          Add User Project
        </Button>
      </Box>

      <DataTable
        columns={columns}
        data={userProjects}
        loading={loading}
        error={error}
        headerEnabled={false}
        serverSidePagination
        onPageChange={(page) => setPage(page)}
        onRowsPerPageChange={(rowsPerPage) => {
          setRowsPerPage(rowsPerPage);
          setPage(0);
        }}
        totalCount={totalCount}
      />
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete User Project</DialogTitle>
        <DialogContent>
          Are you sure you want to delete this user project configuration?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            color='error'
            variant='contained'
            onClick={async () => {
              try {
                await deleteUserProject(projectToDelete.id);
                setDeleteDialogOpen(false);
                setProjectToDelete(null);
                fetchUserProjects({ page, rowsPerPage });
                notifyUser('User project configuration deleted successfully', 'success');
              } catch (e) {
                setError('Failed to delete user project configuration');
                notifyUser('Failed to delete user project configuration ' + e.message, 'error');
              }
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
      <UserProjectForm
        open={projectFormOpen}
        onClose={() => {
          setProjectFormOpen(false);
          setProjectFormEditMode(false);
          setProjectFormInitialValues({});
        }}
        isEdit={projectFormEditMode}
        initialValues={projectFormInitialValues}
        onSave={async (formData) => {
          try {
            if (projectFormEditMode) {
              await updateUserProject(projectFormInitialValues.id, formData);
              notifyUser('User project configuration updated successfully', 'success');
            } else {
              await createUserProject(formData);
              notifyUser('User project configuration created successfully', 'success');
            }
            setProjectFormOpen(false);
            setProjectFormEditMode(false);
            setProjectFormInitialValues({});
            fetchUserProjects({ page, rowsPerPage });
          } catch (e) {
            setError(e.message);
            notifyUser('Failed to save user project configuration ' + e.message, 'error');
          }
        }}
      />
    </Box>
  );
};

export default UserProjectsListing;

import React, { createContext, useContext, useState, useEffect } from 'react';
import { listProjects } from '../services/projectsService';
import { SELECTED_PROJECT_KEY } from '../utils/Constants';

const ProjectContext = createContext();

export const useProject = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};

export const ProjectProvider = ({ children }) => {
  const [projects, setProjects] = useState([]);
  const [selectedProjectId, setSelectedProjectId] = useState(() => {
    return localStorage.getItem(SELECTED_PROJECT_KEY) || '';
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const updateSelectedProjectId = (id) => {
    setSelectedProjectId(id);
    localStorage.setItem(SELECTED_PROJECT_KEY, id);
  };

  // Fetch projects function that can be called manually
  const fetchProjects = async () => {
    setLoading(true);
    setError(null);
    try {
      // Add a small delay to show loading state (better UX)
      await new Promise((resolve) => setTimeout(resolve, 500));

      const response = await listProjects();
      const projectItems = response.items || [];

      if (projectItems.length === 0) {
        // If no projects were returned, use mock data for demonstration
        console.log('No projects found, using mock data');
        const mockProjects = [
          {
            id: '1',
            title: 'Charan - Testing',
            status: 'Active',
            language: 'Hindi',
            region: 'Nashik',
          },
          {
            id: '2',
            title: 'Voice Bot Demo',
            status: 'Active',
            language: 'English',
            region: 'Bangalore',
          },
        ];
        setProjects(mockProjects);

        // If no project is selected, select the first mock project
        if (!selectedProjectId) {
          updateSelectedProjectId(mockProjects[0].id);
        }
      } else {
        setProjects(projectItems);

        // If we have a selectedProjectId but no matching project, and there are projects available,
        // select the first project
        if (
          selectedProjectId &&
          projectItems.length > 0 &&
          !projectItems.find((p) => p.id === selectedProjectId)
        ) {
          updateSelectedProjectId(projectItems[0].id);
        }
        // If no project is selected and there are projects available, select the first one
        else if (!selectedProjectId && projectItems.length > 0) {
          updateSelectedProjectId(projectItems[0].id);
        }
      }
    } catch (err) {
      console.error('Error loading projects:', err);
      setError('Failed to load projects. Please try refreshing the page.');

      // If in development mode, use mock data even on error
      if (process.env.NODE_ENV === 'development') {
        console.log('Using mock data due to error');
        const mockProjects = [
          {
            id: '1',
            title: 'Charan - Testing',
            status: 'Active',
            language: 'Hindi',
            region: 'Nashik',
          },
        ];
        setProjects(mockProjects);

        // If no project is selected, select the first mock project
        if (!selectedProjectId) {
          updateSelectedProjectId(mockProjects[0].id);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch projects when the component mounts
  useEffect(() => {
    fetchProjects();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Find the selected project from the projects array
  const selectedProject = projects.find((p) => p.id === selectedProjectId);

  const value = {
    projects,
    selectedProjectId,
    selectedProject,
    loading,
    error,
    setSelectedProjectId: updateSelectedProjectId,
    refreshProjects: fetchProjects,
  };

  return <ProjectContext.Provider value={value}>{children}</ProjectContext.Provider>;
};

import axios from 'axios';
import { apiService } from './base';

// API base URLs
const API = {
  baseAPI: process.env.REACT_APP_VG_VOICE_BE_HOST,
};

/**
 * Google authentication API call
 * Exchange Google token for a backend authentication token
 * @param {Object} params - Google auth data including id_token
 * @returns {Promise} API response with backend authentication token
 */
export const googleLogin = (params) => {
  // Pass a special option to return the full response including headers
  return axios.post(`${API.baseAPI}/voice/api/v1/users/google_authorization`, params, {
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      dcid: '147',
    },
    // Return full response so we can access headers
    transformResponse: [(data) => data],
  });
};

/**
 * Get current user profile information
 * @returns {Promise} API response with user profile data
 */
export const getUserProfile = () => {
  return apiService.get('voice/api/v1/users/current_user_profile');
};

/**
 * Get current authenticated user's profile
 * This endpoint is used to validate the session and get user data
 * @returns {Promise} API response with complete user profile data
 */
// export const getCurrentUserProfile = () => {
//   return apiService.get('voice/api/v1/users/current_user_profile');
// };

/**
 * Log out the current user
 * @returns {Promise} API response
 */
export const userLogout = () => {
  return apiService.post('voice/api/v1/users/logout');
};

export const getUsers = (params = {}) => {
  return apiService.get('/voice/api/v1/users', { params });
};

export const getAllUsers = () => {
  return apiService.get('/voice/api/v1/users/list_all');
};

export const getRoles = () => {
  return apiService.get('/voice/api/v1/users/roles');
};

export const createUser = (data) => {
  return apiService.post('/voice/api/v1/users', data);
};

export const deleteUser = (id) => {
  return apiService.delete(`/voice/api/v1/users/${id}`);
};

export const getUserById = (id) => {
  return apiService.get(`/voice/api/v1/users/${id}`);
};

export const updateUser = (id, data) => {
  return apiService.put(`/voice/api/v1/users/${id}`, data);
};

export const getUserProjects = (params = {}) => {
  return apiService.get('/voice/api/v1/user_projects', { params });
};

export const createUserProject = (data) => {
  return apiService.post('/voice/api/v1/user_projects', data);
};

export const deleteUserProject = (id) => {
  return apiService.delete(`/voice/api/v1/user_projects/${id}`);
};

export const updateUserProject = (id, data) => {
  return apiService.put(`/voice/api/v1/user_projects/${id}`, data);
};

export const getUserProjectById = (id) => {
  return apiService.get(`/voice/api/v1/user_projects/${id}`);
};
